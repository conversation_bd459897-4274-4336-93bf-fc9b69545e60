using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using Technoloway.Core.Interfaces;
using Technoloway.Core.Models;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize(Policy = "RequireAdminRole")]
[Route("Admin/[controller]/[action]")]
public class DataUploadController : Controller
{
    private readonly IDataUploadService _dataUploadService;
    private readonly UserManager<IdentityUser> _userManager;
    private readonly ILogger<DataUploadController> _logger;

    public DataUploadController(
        IDataUploadService dataUploadService,
        UserManager<IdentityUser> userManager,
        ILogger<DataUploadController> logger)
    {
        _dataUploadService = dataUploadService;
        _userManager = userManager;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        try
        {
            var entities = await _dataUploadService.GetAvailableEntitiesAsync();
            ViewBag.AvailableEntities = entities;
            return View();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading data upload page");
            TempData["ErrorMessage"] = "Failed to load data upload page. Please try again.";
            return RedirectToAction("Index", "Home");
        }
    }

    [HttpPost]
    public async Task<IActionResult> ValidateFile(IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return Json(new { success = false, message = "Please select a file to upload." });
            }

            var (isValid, errorMessage) = await _dataUploadService.ValidateFileAsync(file);
            if (!isValid)
            {
                return Json(new { success = false, message = errorMessage });
            }

            return Json(new { success = true, message = "File is valid and ready for upload." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating file {FileName}", file?.FileName);
            return Json(new { success = false, message = "File validation failed due to an internal error." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> PreviewFile(IFormFile file, bool hasHeaders = true, string? delimiter = null)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return Json(new { success = false, message = "Please select a file to preview." });
            }

            var preview = await _dataUploadService.ParseFilePreviewAsync(file, hasHeaders, delimiter);
            
            return Json(new { 
                success = true, 
                preview = new {
                    fileName = preview.FileName,
                    fileType = preview.FileType,
                    fileSizeBytes = preview.FileSizeBytes,
                    totalRows = preview.TotalRows,
                    headers = preview.Headers,
                    sampleData = preview.SampleData,
                    detectedIssues = preview.DetectedIssues
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error previewing file {FileName}", file?.FileName);
            return Json(new { success = false, message = $"File preview failed: {ex.Message}" });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetEntityInfo(string entityName)
    {
        try
        {
            var entityInfo = await _dataUploadService.GetEntityInfoAsync(entityName);
            if (entityInfo == null)
            {
                return Json(new { success = false, message = "Entity not found." });
            }

            return Json(new { 
                success = true, 
                entity = new {
                    entityName = entityInfo.EntityName,
                    displayName = entityInfo.DisplayName,
                    fields = entityInfo.Fields.Select(f => new {
                        fieldName = f.FieldName,
                        displayName = f.DisplayName,
                        dataType = f.DataType,
                        isRequired = f.IsRequired,
                        isIdentity = f.IsIdentity,
                        isPrimaryKey = f.IsPrimaryKey,
                        maxLength = f.MaxLength,
                        allowedValues = f.AllowedValues
                    }),
                    supportsInsert = entityInfo.SupportsInsert,
                    supportsUpdate = entityInfo.SupportsUpdate,
                    supportsUpsert = entityInfo.SupportsUpsert,
                    supportsTruncate = entityInfo.SupportsTruncate
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting entity info for {EntityName}", entityName);
            return Json(new { success = false, message = "Failed to get entity information." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> ValidateData(IFormFile file, string targetEntity, string fieldMappingsJson, bool hasHeaders = true)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return Json(new { success = false, message = "Please select a file to validate." });
            }

            if (string.IsNullOrEmpty(targetEntity))
            {
                return Json(new { success = false, message = "Please select a target entity." });
            }

            var fieldMappings = JsonSerializer.Deserialize<Dictionary<string, string>>(fieldMappingsJson ?? "{}") ?? new Dictionary<string, string>();

            var validationResult = await _dataUploadService.ValidateDataAsync(file, targetEntity, fieldMappings, hasHeaders);

            return Json(new {
                success = true, // Always return success for validation endpoint
                validation = new {
                    isValid = validationResult.IsValid,
                    errors = validationResult.Errors.Select(e => new {
                        rowNumber = e.RowNumber,
                        fieldName = e.FieldName,
                        errorMessage = e.ErrorMessage,
                        value = e.Value
                    }),
                    warnings = validationResult.Warnings.Select(w => new {
                        rowNumber = w.RowNumber,
                        fieldName = w.FieldName,
                        warningMessage = w.WarningMessage,
                        value = w.Value
                    })
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating data for entity {EntityName}", targetEntity);
            return Json(new { success = false, message = $"Data validation failed: {ex.Message}" });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ProcessUpload(DataUploadViewModel model)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                return Json(new { success = false, message = string.Join(", ", errors) });
            }

            if (model.UploadFile == null || model.UploadFile.Length == 0)
            {
                return Json(new { success = false, message = "Please select a file to upload." });
            }

            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "User not found." });
            }

            var operation = Enum.Parse<DataUploadOperation>(model.Operation);

            var result = await _dataUploadService.ProcessDataUploadAsync(
                model.UploadFile,
                model.TargetEntity,
                operation,
                model.FieldMappings,
                user.Id,
                user.UserName ?? "Unknown",
                model.HasHeaders,
                model.ValidateData,
                model.CreateBackup
            );

            if (result.Success)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }

            return Json(new { 
                success = result.Success, 
                message = result.Message,
                result = new {
                    recordsProcessed = result.RecordsProcessed,
                    recordsSuccessful = result.RecordsSuccessful,
                    recordsFailed = result.RecordsFailed,
                    errors = result.Errors,
                    warnings = result.Warnings,
                    logId = result.LogId,
                    canRollback = result.CanRollback
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing data upload");
            return Json(new { success = false, message = $"Upload processing failed: {ex.Message}" });
        }
    }

    public async Task<IActionResult> History(int page = 1, int pageSize = 20)
    {
        try
        {
            var logs = await _dataUploadService.GetUploadHistoryAsync(page, pageSize);
            ViewBag.CurrentPage = page;
            ViewBag.PageSize = pageSize;
            return View(logs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading upload history");
            TempData["ErrorMessage"] = "Failed to load upload history. Please try again.";
            return RedirectToAction("Index");
        }
    }

    public async Task<IActionResult> Details(int id)
    {
        try
        {
            var log = await _dataUploadService.GetUploadLogAsync(id);
            if (log == null)
            {
                TempData["ErrorMessage"] = "Upload log not found.";
                return RedirectToAction("History");
            }

            return View(log);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading upload log details for ID {LogId}", id);
            TempData["ErrorMessage"] = "Failed to load upload details. Please try again.";
            return RedirectToAction("History");
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Rollback(int id)
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "User not found." });
            }

            var (success, message) = await _dataUploadService.RollbackUploadAsync(id, user.Id);

            if (success)
            {
                TempData["SuccessMessage"] = message;
            }
            else
            {
                TempData["ErrorMessage"] = message;
            }

            return Json(new { success, message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rolling back upload {LogId}", id);
            var errorMessage = "Rollback failed due to an internal error.";
            TempData["ErrorMessage"] = errorMessage;
            return Json(new { success = false, message = errorMessage });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CleanupOldLogs(int daysToKeep = 30)
    {
        try
        {
            await _dataUploadService.CleanupOldLogsAsync(daysToKeep);
            TempData["SuccessMessage"] = $"Successfully cleaned up upload logs older than {daysToKeep} days.";
            return Json(new { success = true, message = "Cleanup completed successfully." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up old upload logs");
            var errorMessage = "Cleanup failed due to an internal error.";
            TempData["ErrorMessage"] = errorMessage;
            return Json(new { success = false, message = errorMessage });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetAvailableEntities()
    {
        try
        {
            var entities = await _dataUploadService.GetAvailableEntitiesAsync();
            return Json(new {
                success = true,
                entities = entities.Select(e => new {
                    entityName = e.EntityName,
                    displayName = e.DisplayName,
                    supportsInsert = e.SupportsInsert,
                    supportsUpdate = e.SupportsUpdate,
                    supportsUpsert = e.SupportsUpsert,
                    supportsTruncate = e.SupportsTruncate
                })
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available entities");
            return Json(new { success = false, message = "Failed to get available entities." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> DebugEntityFields(string entityName)
    {
        try
        {
            var entityInfo = await _dataUploadService.GetEntityInfoAsync(entityName);
            if (entityInfo == null)
            {
                return Json(new { success = false, message = "Entity not found." });
            }

            _logger.LogInformation("Debug: Entity {EntityName} has {FieldCount} fields",
                entityName, entityInfo.Fields.Count);

            foreach (var field in entityInfo.Fields)
            {
                _logger.LogInformation("Debug: Field {FieldName} - Type: {DataType}, Required: {IsRequired}, FK: {IsForeignKey}",
                    field.FieldName, field.DataType, field.IsRequired, field.IsForeignKey);
            }

            return Json(new {
                success = true,
                entityName = entityInfo.EntityName,
                fieldCount = entityInfo.Fields.Count,
                fields = entityInfo.Fields.Select(f => new {
                    fieldName = f.FieldName,
                    displayName = f.DisplayName,
                    dataType = f.DataType,
                    isRequired = f.IsRequired,
                    isIdentity = f.IsIdentity,
                    isPrimaryKey = f.IsPrimaryKey,
                    isForeignKey = f.IsForeignKey,
                    maxLength = f.MaxLength
                })
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error debugging entity fields for {EntityName}", entityName);
            return Json(new { success = false, message = "Failed to debug entity fields." });
        }
    }

    [HttpGet]
    public IActionResult TestEntityProperties(string entityName)
    {
        try
        {
            Type? entityType = null;

            switch (entityName?.ToLower())
            {
                case "category":
                    entityType = typeof(Technoloway.Core.Entities.Category);
                    break;
                case "service":
                    entityType = typeof(Technoloway.Core.Entities.Service);
                    break;
                case "serviceoption":
                    entityType = typeof(Technoloway.Core.Entities.ServiceOption);
                    break;
                case "serviceoptionfeature":
                    entityType = typeof(Technoloway.Core.Entities.ServiceOptionFeature);
                    break;
                default:
                    return Json(new { success = false, message = "Unknown entity name." });
            }

            var properties = entityType.GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance)
                .Select(p => new {
                    name = p.Name,
                    type = p.PropertyType.Name,
                    fullType = p.PropertyType.FullName,
                    isGeneric = p.PropertyType.IsGenericType,
                    isPrimitive = p.PropertyType.IsPrimitive,
                    isValueType = p.PropertyType.IsValueType,
                    isEnum = p.PropertyType.IsEnum,
                    genericTypeDef = p.PropertyType.IsGenericType ? p.PropertyType.GetGenericTypeDefinition().Name : null
                })
                .ToList();

            return Json(new {
                success = true,
                entityName = entityName,
                entityType = entityType.FullName,
                propertyCount = properties.Count,
                properties = properties
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing entity properties for {EntityName}", entityName);
            return Json(new { success = false, message = ex.Message });
        }
    }

}
