// Data Upload Manager JavaScript

class DataUploadManager {
    constructor() {
        this.currentStep = 1;
        this.selectedFile = null;
        this.filePreview = null;
        this.entityInfo = null;
        this.fieldMappings = {};
        this.validationResult = null;
        
        this.initializeEventHandlers();
        this.setupFileDropZone();
    }

    initializeEventHandlers() {
        // File input change
        $('#uploadFile').on('change', (e) => this.handleFileSelect(e.target.files[0]));
        
        // Step navigation
        $('#nextStep1').on('click', () => this.goToStep(2));
        $('#nextStep2').on('click', () => this.goToStep(3));
        $('#nextStep3').on('click', () => this.goToStep(4));
        $('#backStep1').on('click', () => this.goToStep(1));
        $('#backStep2').on('click', () => this.goToStep(2));
        $('#backStep3').on('click', () => this.goToStep(3));
        
        // Action buttons
        $('#previewBtn').on('click', () => this.previewFile());
        $('#validateBtn').on('click', () => this.validateData());
        
        // Entity selection change
        $('#targetEntity').on('change', () => this.handleEntityChange());
        
        // Form submission
        $('#uploadForm').on('submit', (e) => this.handleFormSubmit(e));
    }

    setupFileDropZone() {
        const dropZone = $('#fileDropZone');
        
        dropZone.on('dragover', (e) => {
            e.preventDefault();
            dropZone.addClass('dragover');
        });
        
        dropZone.on('dragleave', (e) => {
            e.preventDefault();
            dropZone.removeClass('dragover');
        });
        
        dropZone.on('drop', (e) => {
            e.preventDefault();
            dropZone.removeClass('dragover');
            
            const files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileSelect(files[0]);
            }
        });
        
        dropZone.on('click', () => {
            $('#uploadFile').click();
        });
    }

    async handleFileSelect(file) {
        if (!file) return;
        
        this.selectedFile = file;
        
        // Validate file
        const formData = new FormData();
        formData.append('file', file);
        
        try {
            const response = await $.ajax({
                url: '/Admin/DataUpload/ValidateFile',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false
            });
            
            if (response.success) {
                this.displayFileInfo(file);
                $('#previewBtn, #nextStep1').prop('disabled', false);
            } else {
                this.showAlert('danger', response.message);
                this.resetFileSelection();
            }
        } catch (error) {
            this.showAlert('danger', 'File validation failed. Please try again.');
            this.resetFileSelection();
        }
    }

    displayFileInfo(file) {
        const fileSize = this.formatFileSize(file.size);
        const fileType = this.getFileType(file.name);
        
        $('#fileDetails').html(`
            <div class="row">
                <div class="col-md-6">
                    <strong>Name:</strong> ${file.name}<br>
                    <strong>Size:</strong> ${fileSize}
                </div>
                <div class="col-md-6">
                    <strong>Type:</strong> ${fileType}<br>
                    <strong>Last Modified:</strong> ${new Date(file.lastModified).toLocaleDateString()}
                </div>
            </div>
        `);
        
        $('#fileInfo').show();
    }

    async previewFile() {
        if (!this.selectedFile) return;
        
        const formData = new FormData();
        formData.append('file', this.selectedFile);
        formData.append('hasHeaders', $('#hasHeaders').is(':checked'));
        
        this.setButtonLoading('#previewBtn', true);
        
        try {
            const response = await $.ajax({
                url: '/Admin/DataUpload/PreviewFile',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false
            });
            
            if (response.success) {
                this.filePreview = response.preview;
                this.displayDataPreview();
            } else {
                this.showAlert('danger', response.message);
            }
        } catch (error) {
            this.showAlert('danger', 'File preview failed. Please try again.');
        } finally {
            this.setButtonLoading('#previewBtn', false);
        }
    }

    displayDataPreview() {
        if (!this.filePreview) return;
        
        const headers = this.filePreview.headers;
        const sampleData = this.filePreview.sampleData;
        
        // Build table
        let tableHtml = '<tr>';
        headers.forEach(header => {
            tableHtml += `<th>${header}</th>`;
        });
        tableHtml += '</tr>';
        
        $('#previewTable thead').html(tableHtml);
        
        let bodyHtml = '';
        sampleData.slice(0, 5).forEach(row => {
            bodyHtml += '<tr>';
            headers.forEach(header => {
                const value = row[header] || '';
                bodyHtml += `<td title="${value}">${this.truncateText(value, 30)}</td>`;
            });
            bodyHtml += '</tr>';
        });
        
        $('#previewTable tbody').html(bodyHtml);
        
        // Summary
        $('#previewSummary').html(`
            <small>
                Showing ${Math.min(5, sampleData.length)} of ${this.filePreview.totalRows} rows. 
                ${headers.length} columns detected.
            </small>
        `);
        
        // Show issues if any
        if (this.filePreview.detectedIssues.length > 0) {
            let issuesHtml = '<div class="alert alert-warning mt-2"><h6>Detected Issues:</h6><ul class="mb-0">';
            this.filePreview.detectedIssues.forEach(issue => {
                issuesHtml += `<li>${issue}</li>`;
            });
            issuesHtml += '</ul></div>';
            $('#previewSummary').append(issuesHtml);
        }
        
        $('#dataPreview').show();
    }

    async handleEntityChange() {
        const entityName = $('#targetEntity').val();
        if (!entityName) {
            $('#nextStep2').prop('disabled', true);
            return;
        }
        
        try {
            const response = await $.ajax({
                url: '/Admin/DataUpload/GetEntityInfo',
                type: 'GET',
                data: { entityName }
            });
            
            if (response.success) {
                this.entityInfo = response.entity;
                this.updateOperationOptions();
                $('#nextStep2').prop('disabled', false);
            } else {
                this.showAlert('danger', response.message);
                $('#nextStep2').prop('disabled', true);
            }
        } catch (error) {
            this.showAlert('danger', 'Failed to get entity information.');
            $('#nextStep2').prop('disabled', true);
        }
    }

    updateOperationOptions() {
        const operations = $('#operation option');
        operations.each((index, option) => {
            const value = $(option).val();
            if (value) {
                const isSupported = this.isOperationSupported(value);
                $(option).prop('disabled', !isSupported);
                if (!isSupported) {
                    $(option).text($(option).text() + ' (Not supported)');
                }
            }
        });
    }

    isOperationSupported(operation) {
        if (!this.entityInfo) return false;
        
        switch (operation) {
            case 'Insert': return this.entityInfo.supportsInsert;
            case 'Update': return this.entityInfo.supportsUpdate;
            case 'Upsert': return this.entityInfo.supportsUpsert;
            case 'TruncateAndInsert': return this.entityInfo.supportsTruncate;
            default: return false;
        }
    }

    goToStep(step) {
        // Hide all steps
        $('.upload-step').hide();
        
        // Show target step
        $(`#step${step}`).show();
        this.currentStep = step;
        
        // Handle step-specific logic
        if (step === 3) {
            this.buildFieldMapping();
        } else if (step === 4) {
            this.buildUploadSummary();
        }
    }

    buildFieldMapping() {
        if (!this.filePreview || !this.entityInfo) return;
        
        const sourceFields = this.filePreview.headers;
        const targetFields = this.entityInfo.fields;
        
        let mappingHtml = '';
        
        sourceFields.forEach(sourceField => {
            mappingHtml += `
                <div class="mapping-row">
                    <div class="col-md-5">
                        <label class="form-label">Source Field</label>
                        <input type="text" class="form-control" value="${sourceField}" readonly>
                    </div>
                    <div class="mapping-arrow">
                        <i class="fas fa-arrow-right"></i>
                    </div>
                    <div class="col-md-5">
                        <label class="form-label">Target Field</label>
                        <select class="form-select mapping-target" data-source="${sourceField}">
                            <option value="">-- Skip this field --</option>
                            ${targetFields.map(field => 
                                `<option value="${field.fieldName}" 
                                    ${this.isFieldMatch(sourceField, field.fieldName) ? 'selected' : ''}
                                    data-required="${field.isRequired}"
                                    data-type="${field.dataType}">
                                    ${field.displayName} (${field.dataType})
                                    ${field.isRequired ? ' *' : ''}
                                </option>`
                            ).join('')}
                        </select>
                    </div>
                </div>
            `;
        });
        
        $('#mappingContainer').html(mappingHtml);
        
        // Update field mappings when selection changes
        $('.mapping-target').on('change', () => this.updateFieldMappings());
        this.updateFieldMappings();
    }

    updateFieldMappings() {
        this.fieldMappings = {};
        
        $('.mapping-target').each((index, select) => {
            const sourceField = $(select).data('source');
            const targetField = $(select).val();
            
            if (targetField) {
                this.fieldMappings[sourceField] = targetField;
            }
        });
        
        // Check if required fields are mapped
        const requiredFields = this.entityInfo.fields.filter(f => f.isRequired && !f.isIdentity);
        const mappedTargets = Object.values(this.fieldMappings);
        const missingRequired = requiredFields.filter(f => !mappedTargets.includes(f.fieldName));
        
        if (missingRequired.length > 0) {
            $('#nextStep3').prop('disabled', true);
            this.showAlert('warning', `Required fields not mapped: ${missingRequired.map(f => f.displayName).join(', ')}`);
        } else {
            $('#nextStep3').prop('disabled', false);
            $('.alert-warning').remove();
        }
    }

    async validateData() {
        if (!this.selectedFile || !this.entityInfo || Object.keys(this.fieldMappings).length === 0) {
            this.showAlert('warning', 'Please complete the field mapping first.');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', this.selectedFile);
        formData.append('targetEntity', this.entityInfo.entityName);
        formData.append('fieldMappingsJson', JSON.stringify(this.fieldMappings));
        formData.append('hasHeaders', $('#hasHeaders').is(':checked'));
        
        this.setButtonLoading('#validateBtn', true);
        
        try {
            const response = await $.ajax({
                url: '/Admin/DataUpload/ValidateData',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false
            });
            
            if (response.success) {
                this.validationResult = response.validation;
                this.displayValidationResults();
                
                if (this.validationResult.isValid) {
                    this.showAlert('success', 'Data validation passed successfully!');
                } else {
                    this.showAlert('warning', 'Data validation found some issues. Please review below.');
                }
            } else {
                this.showAlert('danger', response.message);
            }
        } catch (error) {
            this.showAlert('danger', 'Data validation failed. Please try again.');
        } finally {
            this.setButtonLoading('#validateBtn', false);
        }
    }

    displayValidationResults() {
        if (!this.validationResult) return;
        
        let resultsHtml = '<h6>Validation Results</h6>';
        
        if (this.validationResult.errors.length > 0) {
            resultsHtml += '<div class="alert alert-danger"><h6>Errors:</h6><ul class="mb-0">';
            this.validationResult.errors.forEach(error => {
                resultsHtml += `<li>Row ${error.rowNumber}: ${error.errorMessage} (Field: ${error.fieldName})</li>`;
            });
            resultsHtml += '</ul></div>';
        }
        
        if (this.validationResult.warnings.length > 0) {
            resultsHtml += '<div class="alert alert-warning"><h6>Warnings:</h6><ul class="mb-0">';
            this.validationResult.warnings.forEach(warning => {
                resultsHtml += `<li>Row ${warning.rowNumber}: ${warning.warningMessage} (Field: ${warning.fieldName})</li>`;
            });
            resultsHtml += '</ul></div>';
        }
        
        if (this.validationResult.isValid) {
            resultsHtml += '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>All data validation checks passed!</div>';
        }
        
        $('#validationResults').html(resultsHtml);
    }

    buildUploadSummary() {
        const operation = $('#operation option:selected').text();
        const entity = $('#targetEntity option:selected').text();
        const recordCount = this.filePreview ? this.filePreview.totalRows : 0;
        const mappedFields = Object.keys(this.fieldMappings).length;
        
        const summaryHtml = `
            <div class="row">
                <div class="col-md-6">
                    <strong>File:</strong> ${this.selectedFile.name}<br>
                    <strong>Target Entity:</strong> ${entity}<br>
                    <strong>Operation:</strong> ${operation}
                </div>
                <div class="col-md-6">
                    <strong>Records to Process:</strong> ${recordCount}<br>
                    <strong>Fields Mapped:</strong> ${mappedFields}<br>
                    <strong>Validation:</strong> ${$('#validateData').is(':checked') ? 'Enabled' : 'Disabled'}
                </div>
            </div>
        `;
        
        $('#uploadSummary').html(summaryHtml);
    }

    async handleFormSubmit(e) {
        e.preventDefault();
        
        if (!this.selectedFile || !this.entityInfo || Object.keys(this.fieldMappings).length === 0) {
            this.showAlert('danger', 'Please complete all required fields.');
            return;
        }
        
        const formData = new FormData();
        formData.append('UploadFile', this.selectedFile);
        formData.append('TargetEntity', this.entityInfo.entityName);
        formData.append('Operation', $('#operation').val());
        formData.append('HasHeaders', $('#hasHeaders').is(':checked'));
        formData.append('ValidateData', $('#validateData').is(':checked'));
        formData.append('CreateBackup', $('#createBackup').is(':checked'));
        
        // Add field mappings
        Object.keys(this.fieldMappings).forEach(key => {
            formData.append(`FieldMappings[${key}]`, this.fieldMappings[key]);
        });
        
        // Add anti-forgery token
        formData.append('__RequestVerificationToken', $('input[name="__RequestVerificationToken"]').val());
        
        this.showUploadProgress();
        this.setButtonLoading('#processBtn', true);
        
        try {
            const response = await $.ajax({
                url: '/Admin/DataUpload/ProcessUpload',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false
            });
            
            this.hideUploadProgress();
            this.displayResults(response);
            
        } catch (error) {
            this.hideUploadProgress();
            this.showAlert('danger', 'Upload processing failed. Please try again.');
        } finally {
            this.setButtonLoading('#processBtn', false);
        }
    }

    showUploadProgress() {
        $('#uploadProgress').show();
        this.updateProgress(0, 'Initializing upload...');
        
        // Simulate progress
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress >= 90) {
                clearInterval(interval);
                this.updateProgress(90, 'Processing data...');
            } else {
                this.updateProgress(progress, 'Uploading and processing...');
            }
        }, 500);
    }

    hideUploadProgress() {
        $('#uploadProgress').hide();
    }

    updateProgress(percent, text) {
        $('#uploadProgress .progress-bar').css('width', `${percent}%`);
        $('#progressText').text(text);
    }

    displayResults(response) {
        let resultsHtml = '';
        
        if (response.success) {
            resultsHtml = `
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle me-2"></i>Upload Completed Successfully!</h5>
                    <p>${response.message}</p>
                </div>
                <div class="row">
                    <div class="col-md-4 text-center">
                        <div class="stat-number text-primary">${response.result.recordsProcessed}</div>
                        <div class="stat-label">Total Records</div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="stat-number text-success">${response.result.recordsSuccessful}</div>
                        <div class="stat-label">Successful</div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="stat-number text-danger">${response.result.recordsFailed}</div>
                        <div class="stat-label">Failed</div>
                    </div>
                </div>
            `;
            
            if (response.result.errors.length > 0) {
                resultsHtml += '<div class="alert alert-warning mt-3"><h6>Errors:</h6><ul class="mb-0">';
                response.result.errors.forEach(error => {
                    resultsHtml += `<li>${error}</li>`;
                });
                resultsHtml += '</ul></div>';
            }
        } else {
            resultsHtml = `
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>Upload Failed</h5>
                    <p>${response.message}</p>
                </div>
            `;
        }
        
        $('#resultsContent').html(resultsHtml);
        $('#resultsModal').modal('show');
    }

    // Utility methods
    isFieldMatch(sourceField, targetField) {
        const source = sourceField.toLowerCase().replace(/[^a-z0-9]/g, '');
        const target = targetField.toLowerCase().replace(/[^a-z0-9]/g, '');
        return source === target || source.includes(target) || target.includes(source);
    }

    formatFileSize(bytes) {
        const sizes = ['B', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 B';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`;
    }

    getFileType(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        const typeMap = {
            'csv': 'CSV',
            'xlsx': 'Excel',
            'xls': 'Excel',
            'json': 'JSON',
            'xml': 'XML',
            'sql': 'SQL',
            'tsv': 'TSV',
            'tab': 'TSV'
        };
        return typeMap[extension] || 'Unknown';
    }

    truncateText(text, maxLength) {
        if (typeof text !== 'string') text = String(text);
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    setButtonLoading(selector, loading) {
        const btn = $(selector);
        if (loading) {
            btn.addClass('loading').prop('disabled', true);
        } else {
            btn.removeClass('loading').prop('disabled', false);
        }
    }

    showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('.container-fluid').prepend(alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            $('.alert').alert('close');
        }, 5000);
    }

    resetFileSelection() {
        this.selectedFile = null;
        this.filePreview = null;
        $('#uploadFile').val('');
        $('#fileInfo').hide();
        $('#dataPreview').hide();
        $('#previewBtn, #nextStep1').prop('disabled', true);
    }
}

// Initialize when document is ready
$(document).ready(() => {
    new DataUploadManager();
});
