@model Technoloway.Core.Entities.DataUploadLog
@{
    ViewData["Title"] = "Upload Details";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="page-title">
                            <i class="fas fa-info-circle me-2"></i>Upload Details
                        </h1>
                        <p class="page-subtitle">Detailed information about the data upload</p>
                    </div>
                    <div>
                        <a href="@Url.Action("History")" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to History
                        </a>
                        @if (Model.CanRollback && !Model.RolledBackAt.HasValue && Model.Status == "Completed")
                        {
                            <button type="button" class="btn btn-warning ms-2" onclick="rollbackUpload(@Model.Id)">
                                <i class="fas fa-undo me-1"></i>Rollback
                            </button>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Upload Summary -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-upload me-2"></i>Upload Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-medium">File Name:</td>
                                    <td>
                                        <i class="@GetFileIcon(Model.FileType) me-2"></i>
                                        @Model.FileName
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">File Type:</td>
                                    <td><span class="badge bg-info">@Model.FileType</span></td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">File Size:</td>
                                    <td>@FormatFileSize(Model.FileSizeBytes)</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Target Entity:</td>
                                    <td><span class="badge bg-primary">@Model.TargetEntity</span></td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Operation:</td>
                                    <td><span class="badge bg-secondary">@Model.Operation</span></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-medium">Status:</td>
                                    <td>
                                        <span class="badge @GetStatusBadgeClass(Model.Status)">
                                            <i class="@GetStatusIcon(Model.Status) me-1"></i>
                                            @Model.Status
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Started At:</td>
                                    <td>@Model.StartedAt.ToString("MMM dd, yyyy HH:mm:ss")</td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Completed At:</td>
                                    <td>
                                        @if (Model.CompletedAt.HasValue)
                                        {
                                            @Model.CompletedAt.Value.ToString("MMM dd, yyyy HH:mm:ss")
                                        }
                                        else
                                        {
                                            <span class="text-muted">Not completed</span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Duration:</td>
                                    <td>
                                        @if (Model.CompletedAt.HasValue)
                                        {
                                            @((Model.CompletedAt.Value - Model.StartedAt).TotalSeconds.ToString("F1")) seconds
                                        }
                                        else
                                        {
                                            <span class="text-muted">In progress</span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-medium">Uploaded By:</td>
                                    <td>
                                        <div>@Model.UploadedByUserName</div>
                                        <small class="text-muted">@Model.UploadedByUserId</small>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <div class="stat-item">
                                <div class="stat-number text-primary">@Model.RecordsProcessed</div>
                                <div class="stat-label">Total Records</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-number text-success">@Model.RecordsSuccessful</div>
                                <div class="stat-label">Successful</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-number text-danger">@Model.RecordsFailed</div>
                                <div class="stat-label">Failed</div>
                            </div>
                        </div>
                    </div>

                    @if (Model.RecordsProcessed > 0)
                    {
                        <div class="progress mt-3">
                            <div class="progress-bar bg-success" style="width: @((double)Model.RecordsSuccessful / Model.RecordsProcessed * 100)%"></div>
                            <div class="progress-bar bg-danger" style="width: @((double)Model.RecordsFailed / Model.RecordsProcessed * 100)%"></div>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                @((double)Model.RecordsSuccessful / Model.RecordsProcessed * 100).ToString("F1")% Success Rate
                            </small>
                        </div>
                    }
                </div>
            </div>

            @if (Model.CanRollback)
            {
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-shield-alt me-2"></i>Backup & Rollback
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (!string.IsNullOrEmpty(Model.BackupFilePath))
                        {
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Backup created and available for rollback.
                            </div>
                        }

                        @if (Model.RolledBackAt.HasValue)
                        {
                            <div class="alert alert-warning">
                                <i class="fas fa-undo me-2"></i>
                                <strong>Rolled Back</strong><br>
                                On: @Model.RolledBackAt.Value.ToString("MMM dd, yyyy HH:mm:ss")<br>
                                By: @Model.RolledBackByUserId
                            </div>
                        }
                        else if (Model.Status == "Completed")
                        {
                            <p class="text-muted">This upload can be rolled back to restore the previous state.</p>
                        }
                    </div>
                </div>
            }
        </div>
    </div>

    <!-- Field Mappings -->
    @if (!string.IsNullOrEmpty(Model.FieldMappings))
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-exchange-alt me-2"></i>Field Mappings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th>Source Field</th>
                                        <th>Target Field</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @{
                                        var mappings = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(Model.FieldMappings);
                                    }
                                    @if (mappings != null)
                                    {
                                        @foreach (var mapping in mappings)
                                        {
                                            <tr>
                                                <td><code>@mapping.Key</code></td>
                                                <td><code>@mapping.Value</code></td>
                                            </tr>
                                        }
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Errors and Validation -->
    @if (!string.IsNullOrEmpty(Model.ErrorDetails) || !string.IsNullOrEmpty(Model.ValidationErrors))
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>Issues & Errors
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (!string.IsNullOrEmpty(Model.ErrorDetails))
                        {
                            <h6>Processing Errors</h6>
                            <div class="alert alert-danger">
                                @{
                                    var errors = System.Text.Json.JsonSerializer.Deserialize<List<string>>(Model.ErrorDetails);
                                }
                                @if (errors != null)
                                {
                                    <ul class="mb-0">
                                        @foreach (var error in errors)
                                        {
                                            <li>@error</li>
                                        }
                                    </ul>
                                }
                                else
                                {
                                    <pre>@Model.ErrorDetails</pre>
                                }
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(Model.ValidationErrors))
                        {
                            <h6>Validation Errors</h6>
                            <div class="alert alert-warning">
                                @{
                                    var validationErrors = System.Text.Json.JsonSerializer.Deserialize<List<object>>(Model.ValidationErrors);
                                }
                                @if (validationErrors != null)
                                {
                                    <ul class="mb-0">
                                        @foreach (var error in validationErrors)
                                        {
                                            <li>@error.ToString()</li>
                                        }
                                    </ul>
                                }
                                else
                                {
                                    <pre>@Model.ValidationErrors</pre>
                                }
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Rollback</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> This will restore the database to its state before this upload.
                    All changes made by this upload will be lost.
                </div>
                <p>Are you sure you want to rollback this upload?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="confirmRollback">Rollback</button>
            </div>
        </div>
    </div>
</div>

@functions {
    private string GetFileIcon(string fileType)
    {
        return fileType switch
        {
            "CSV" => "fas fa-file-csv text-success",
            "Excel" => "fas fa-file-excel text-success",
            "JSON" => "fas fa-file-code text-info",
            "XML" => "fas fa-file-code text-warning",
            "SQL" => "fas fa-database text-primary",
            "TSV" => "fas fa-file-alt text-secondary",
            _ => "fas fa-file text-muted"
        };
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "Completed" => "bg-success",
            "Processing" => "bg-primary",
            "Failed" => "bg-danger",
            "Rolled Back" => "bg-warning",
            _ => "bg-secondary"
        };
    }

    private string GetStatusIcon(string status)
    {
        return status switch
        {
            "Completed" => "fas fa-check-circle",
            "Processing" => "fas fa-spinner fa-spin",
            "Failed" => "fas fa-exclamation-triangle",
            "Rolled Back" => "fas fa-undo",
            _ => "fas fa-clock"
        };
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }
}

@section Scripts {
    <script>
        function rollbackUpload(logId) {
            $('#confirmRollback').off('click').on('click', function() {
                performRollback(logId);
            });
            $('#confirmModal').modal('show');
        }

        function performRollback(logId) {
            $.post('@Url.Action("Rollback")', {
                id: logId,
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
            })
            .done(function(response) {
                $('#confirmModal').modal('hide');
                if (response.success) {
                    showAlert('success', response.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showAlert('danger', response.message);
                }
            })
            .fail(function() {
                $('#confirmModal').modal('hide');
                showAlert('danger', 'Rollback request failed. Please try again.');
            });
        }

        function showAlert(type, message) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('.container-fluid').prepend(alertHtml);
        }
    </script>
}
