using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Core.Models;
using Technoloway.Infrastructure.Data;

namespace Technoloway.Infrastructure.Services;

public class DataUploadService : IDataUploadService
{
    private readonly ApplicationDbContext _context;
    private readonly IDataParsingService _parsingService;
    private readonly IDatabaseOperationService _databaseService;
    private readonly ILogger<DataUploadService> _logger;

    public DataUploadService(
        ApplicationDbContext context,
        IDataParsingService parsingService,
        IDatabaseOperationService databaseService,
        ILogger<DataUploadService> logger)
    {
        _context = context;
        _parsingService = parsingService;
        _databaseService = databaseService;
        _logger = logger;
    }

    public async Task<(bool IsValid, string ErrorMessage)> ValidateFileAsync(IFormFile file)
    {
        try
        {
            // Check file size (max 100MB)
            if (file.Length > 100 * 1024 * 1024)
            {
                return (false, "File size exceeds 100MB limit");
            }

            // Check file extension
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            var supportedExtensions = SupportedFileTypes.FileExtensions.Values.SelectMany(x => x).ToList();

            if (!supportedExtensions.Contains(extension))
            {
                return (false, $"Unsupported file type. Supported types: {string.Join(", ", supportedExtensions)}");
            }

            // Validate file content
            using var stream = file.OpenReadStream();
            var fileType = _parsingService.DetectFileType(file.FileName, stream);
            var (isValid, issues) = await _parsingService.ValidateFileContentAsync(stream, fileType);

            if (!isValid)
            {
                return (false, $"File validation failed: {string.Join(", ", issues)}");
            }

            return (true, string.Empty);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating file {FileName}", file.FileName);
            return (false, "File validation failed due to an internal error");
        }
    }

    public async Task<FilePreviewModel> ParseFilePreviewAsync(IFormFile file, bool hasHeaders = true, string? delimiter = null)
    {
        try
        {
            using var stream = file.OpenReadStream();
            var fileType = _parsingService.DetectFileType(file.FileName, stream);

            var (data, headers) = fileType switch
            {
                "CSV" => await _parsingService.ParseCsvAsync(stream, hasHeaders, delimiter ?? ","),
                "Excel" => await _parsingService.ParseExcelAsync(stream, hasHeaders),
                "JSON" => await _parsingService.ParseJsonAsync(stream),
                "XML" => await _parsingService.ParseXmlAsync(stream),
                "SQL" => await _parsingService.ParseSqlAsync(stream, "temp_table"),
                "TSV" => await _parsingService.ParseCsvAsync(stream, hasHeaders, "\t"),
                _ => throw new NotSupportedException($"File type '{fileType}' is not supported")
            };

            var preview = new FilePreviewModel
            {
                FileName = file.FileName,
                FileType = fileType,
                FileSizeBytes = file.Length,
                TotalRows = data.Count,
                Headers = headers,
                SampleData = data.Take(10).ToList(), // Show first 10 rows
                DetectedIssues = new List<string>()
            };

            // Detect potential issues
            if (data.Count == 0)
            {
                preview.DetectedIssues.Add("No data rows found in the file");
            }

            if (headers.Count == 0)
            {
                preview.DetectedIssues.Add("No headers detected in the file");
            }

            // Check for empty columns
            foreach (var header in headers)
            {
                var hasData = data.Any(row => row.ContainsKey(header) && 
                    !string.IsNullOrWhiteSpace(row[header]?.ToString()));
                
                if (!hasData)
                {
                    preview.DetectedIssues.Add($"Column '{header}' appears to be empty");
                }
            }

            return preview;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing file preview for {FileName}", file.FileName);
            throw new InvalidOperationException($"Failed to parse file preview: {ex.Message}", ex);
        }
    }

    public async Task<List<DatabaseEntityInfo>> GetAvailableEntitiesAsync()
    {
        var entities = new List<DatabaseEntityInfo>();

        try
        {
            // Get all entity types that inherit from BaseEntity
            var entityTypes = typeof(BaseEntity).Assembly.GetTypes()
                .Where(t => t.IsSubclassOf(typeof(BaseEntity)) && !t.IsAbstract)
                .ToList();

            foreach (var entityType in entityTypes)
            {
                var entityInfo = await _databaseService.GetEntityMetadataAsync(entityType.Name);
                entities.Add(entityInfo);
            }

            return entities.OrderBy(e => e.DisplayName).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available entities");
            throw;
        }
    }

    public async Task<DatabaseEntityInfo?> GetEntityInfoAsync(string entityName)
    {
        try
        {
            return await _databaseService.GetEntityMetadataAsync(entityName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting entity info for {EntityName}", entityName);
            return null;
        }
    }

    public async Task<ValidationResult> ValidateDataAsync(
        IFormFile file, 
        string targetEntity, 
        Dictionary<string, string> fieldMappings, 
        bool hasHeaders = true)
    {
        try
        {
            using var stream = file.OpenReadStream();
            var fileType = _parsingService.DetectFileType(file.FileName, stream);

            var (data, headers) = fileType switch
            {
                "CSV" => await _parsingService.ParseCsvAsync(stream, hasHeaders, ","),
                "Excel" => await _parsingService.ParseExcelAsync(stream, hasHeaders),
                "JSON" => await _parsingService.ParseJsonAsync(stream),
                "XML" => await _parsingService.ParseXmlAsync(stream),
                "SQL" => await _parsingService.ParseSqlAsync(stream, targetEntity),
                "TSV" => await _parsingService.ParseCsvAsync(stream, hasHeaders, "\t"),
                _ => throw new NotSupportedException($"File type '{fileType}' is not supported")
            };

            return await _databaseService.ValidateDataConstraintsAsync(targetEntity, data, fieldMappings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating data for entity {EntityName}", targetEntity);
            
            var result = new ValidationResult { IsValid = false };
            result.Errors.Add(new ValidationError
            {
                RowNumber = 0,
                FieldName = "File",
                ErrorMessage = $"Data validation failed: {ex.Message}"
            });
            
            return result;
        }
    }

    public async Task<DataUploadResult> ProcessDataUploadAsync(
        IFormFile file, 
        string targetEntity, 
        DataUploadOperation operation, 
        Dictionary<string, string> fieldMappings, 
        string userId, 
        string userName, 
        bool hasHeaders = true, 
        bool validateData = true, 
        bool createBackup = true)
    {
        var uploadLog = new DataUploadLog
        {
            FileName = file.FileName,
            FileType = _parsingService.DetectFileType(file.FileName, file.OpenReadStream()),
            FileSizeBytes = file.Length,
            TargetEntity = targetEntity,
            Operation = operation.ToString(),
            Status = DataUploadStatus.Processing.ToString(),
            UploadedByUserId = userId,
            UploadedByUserName = userName,
            StartedAt = DateTime.UtcNow,
            FieldMappings = JsonSerializer.Serialize(fieldMappings),
            CanRollback = createBackup,
            CreatedAt = DateTime.UtcNow,
            IsDeleted = false
        };

        try
        {
            // Save initial log
            _context.DataUploadLogs.Add(uploadLog);
            await _context.SaveChangesAsync();

            // Parse file data
            using var stream = file.OpenReadStream();
            var (data, headers) = uploadLog.FileType switch
            {
                "CSV" => await _parsingService.ParseCsvAsync(stream, hasHeaders, ","),
                "Excel" => await _parsingService.ParseExcelAsync(stream, hasHeaders),
                "JSON" => await _parsingService.ParseJsonAsync(stream),
                "XML" => await _parsingService.ParseXmlAsync(stream),
                "SQL" => await _parsingService.ParseSqlAsync(stream, targetEntity),
                "TSV" => await _parsingService.ParseCsvAsync(stream, hasHeaders, "\t"),
                _ => throw new NotSupportedException($"File type '{uploadLog.FileType}' is not supported")
            };

            uploadLog.RecordsProcessed = data.Count;

            // Validate data if requested
            if (validateData)
            {
                var validationResult = await _databaseService.ValidateDataConstraintsAsync(targetEntity, data, fieldMappings);
                if (!validationResult.IsValid)
                {
                    uploadLog.Status = DataUploadStatus.Failed.ToString();
                    uploadLog.ValidationErrors = JsonSerializer.Serialize(validationResult.Errors);
                    uploadLog.CompletedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();

                    return new DataUploadResult
                    {
                        Success = false,
                        Message = "Data validation failed",
                        RecordsProcessed = data.Count,
                        RecordsSuccessful = 0,
                        RecordsFailed = data.Count,
                        Errors = validationResult.Errors.Select(e => $"Row {e.RowNumber}: {e.ErrorMessage}").ToList(),
                        LogId = uploadLog.Id.ToString()
                    };
                }
            }

            // Create backup if requested
            if (createBackup)
            {
                try
                {
                    uploadLog.BackupFilePath = await _databaseService.CreateBackupAsync(targetEntity);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to create backup for entity {EntityName}", targetEntity);
                }
            }

            // Perform database operation
            var (successful, failed, errors) = operation switch
            {
                DataUploadOperation.Insert => await _databaseService.InsertDataAsync(targetEntity, data, fieldMappings),
                DataUploadOperation.Update => await _databaseService.UpdateDataAsync(targetEntity, data, fieldMappings, "Id"),
                DataUploadOperation.Upsert => await _databaseService.UpsertDataAsync(targetEntity, data, fieldMappings, "Id"),
                DataUploadOperation.TruncateAndInsert => await _databaseService.TruncateAndInsertAsync(targetEntity, data, fieldMappings),
                _ => throw new NotSupportedException($"Operation '{operation}' is not supported")
            };

            // Update log with results
            uploadLog.RecordsSuccessful = successful;
            uploadLog.RecordsFailed = failed;
            uploadLog.Status = failed > 0 ? DataUploadStatus.Completed.ToString() : DataUploadStatus.Completed.ToString();
            uploadLog.CompletedAt = DateTime.UtcNow;

            if (errors.Any())
            {
                uploadLog.ErrorDetails = JsonSerializer.Serialize(errors);
            }

            await _context.SaveChangesAsync();

            return new DataUploadResult
            {
                Success = true,
                Message = $"Upload completed. {successful} records successful, {failed} records failed.",
                RecordsProcessed = data.Count,
                RecordsSuccessful = successful,
                RecordsFailed = failed,
                Errors = errors,
                LogId = uploadLog.Id.ToString(),
                CanRollback = createBackup && !string.IsNullOrEmpty(uploadLog.BackupFilePath)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing data upload for entity {EntityName}", targetEntity);

            uploadLog.Status = DataUploadStatus.Failed.ToString();
            uploadLog.ErrorDetails = ex.Message;
            uploadLog.CompletedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            return new DataUploadResult
            {
                Success = false,
                Message = $"Upload failed: {ex.Message}",
                RecordsProcessed = uploadLog.RecordsProcessed,
                RecordsSuccessful = 0,
                RecordsFailed = uploadLog.RecordsProcessed,
                Errors = new List<string> { ex.Message },
                LogId = uploadLog.Id.ToString()
            };
        }
    }

    public async Task<List<DataUploadLog>> GetUploadHistoryAsync(int page = 1, int pageSize = 20)
    {
        try
        {
            return await _context.DataUploadLogs
                .Where(log => !log.IsDeleted)
                .OrderByDescending(log => log.StartedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting upload history");
            throw;
        }
    }

    public async Task<DataUploadLog?> GetUploadLogAsync(int logId)
    {
        try
        {
            return await _context.DataUploadLogs
                .FirstOrDefaultAsync(log => log.Id == logId && !log.IsDeleted);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting upload log {LogId}", logId);
            return null;
        }
    }

    public async Task<(bool Success, string Message)> RollbackUploadAsync(int logId, string userId)
    {
        try
        {
            var uploadLog = await GetUploadLogAsync(logId);
            if (uploadLog == null)
            {
                return (false, "Upload log not found");
            }

            if (!uploadLog.CanRollback || string.IsNullOrEmpty(uploadLog.BackupFilePath))
            {
                return (false, "Rollback is not available for this upload");
            }

            if (uploadLog.RolledBackAt.HasValue)
            {
                return (false, "This upload has already been rolled back");
            }

            var success = await _databaseService.RestoreFromBackupAsync(uploadLog.TargetEntity, uploadLog.BackupFilePath);
            if (success)
            {
                uploadLog.Status = DataUploadStatus.RolledBack.ToString();
                uploadLog.RolledBackAt = DateTime.UtcNow;
                uploadLog.RolledBackByUserId = userId;
                await _context.SaveChangesAsync();

                return (true, "Upload successfully rolled back");
            }

            return (false, "Failed to restore from backup");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rolling back upload {LogId}", logId);
            return (false, $"Rollback failed: {ex.Message}");
        }
    }

    public async Task CleanupOldLogsAsync(int daysToKeep = 30)
    {
        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-daysToKeep);
            var oldLogs = await _context.DataUploadLogs
                .Where(log => log.CreatedAt < cutoffDate)
                .ToListAsync();

            foreach (var log in oldLogs)
            {
                // Delete backup files
                if (!string.IsNullOrEmpty(log.BackupFilePath) && File.Exists(log.BackupFilePath))
                {
                    try
                    {
                        File.Delete(log.BackupFilePath);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to delete backup file {BackupFilePath}", log.BackupFilePath);
                    }
                }

                // Soft delete the log
                log.IsDeleted = true;
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("Cleaned up {Count} old upload logs", oldLogs.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up old upload logs");
        }
    }
}
