@model Technoloway.Core.Models.DataUploadViewModel
@{
    ViewData["Title"] = "Data Upload Manager";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="page-title">
                            <i class="fas fa-upload me-2"></i>Data Upload Manager
                        </h1>
                        <p class="page-subtitle">Upload and process data files to seed or update your database</p>
                    </div>
                    <div>
                        <a href="@Url.Action("History")" class="btn btn-outline-primary">
                            <i class="fas fa-history me-1"></i>Upload History
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload Progress -->
    <div id="uploadProgress" class="row mb-4" style="display: none;">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-spinner fa-spin me-2"></i>Processing Upload
                    </h5>
                    <div class="progress mb-2">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <p class="mb-0" id="progressText">Initializing...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-upload me-2"></i>Upload Data File
                    </h5>
                </div>
                <div class="card-body">
                    <form id="uploadForm" enctype="multipart/form-data">
                        @Html.AntiForgeryToken()
                        
                        <!-- Step 1: File Selection -->
                        <div class="upload-step" id="step1">
                            <h6 class="step-title">
                                <span class="step-number">1</span>
                                Select File
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="file-drop-zone" id="fileDropZone">
                                        <div class="drop-zone-content">
                                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                            <h5>Drag & Drop your file here</h5>
                                            <p class="text-muted">or click to browse</p>
                                            <input type="file" id="uploadFile" name="UploadFile" class="d-none" 
                                                   accept=".csv,.xlsx,.xls,.json,.xml,.sql,.tsv,.tab" />
                                            <button type="button" class="btn btn-primary" onclick="document.getElementById('uploadFile').click()">
                                                <i class="fas fa-folder-open me-1"></i>Browse Files
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="supported-formats">
                                        <h6>Supported Formats:</h6>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-file-csv text-success me-2"></i>CSV (.csv)</li>
                                            <li><i class="fas fa-file-excel text-success me-2"></i>Excel (.xlsx, .xls)</li>
                                            <li><i class="fas fa-file-code text-success me-2"></i>JSON (.json)</li>
                                            <li><i class="fas fa-file-code text-success me-2"></i>XML (.xml)</li>
                                            <li><i class="fas fa-database text-success me-2"></i>SQL (.sql)</li>
                                            <li><i class="fas fa-file-alt text-success me-2"></i>TSV (.tsv, .tab)</li>
                                        </ul>
                                        <small class="text-muted">Maximum file size: 100MB</small>
                                    </div>
                                </div>
                            </div>

                            <div id="fileInfo" class="mt-3" style="display: none;">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>File Information</h6>
                                    <div id="fileDetails"></div>
                                </div>
                            </div>

                            <div class="mt-3">
                                <button type="button" id="previewBtn" class="btn btn-outline-primary" disabled>
                                    <i class="fas fa-eye me-1"></i>Preview Data
                                </button>
                                <button type="button" id="nextStep1" class="btn btn-primary ms-2" disabled>
                                    Next <i class="fas fa-arrow-right ms-1"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Step 2: Data Preview & Configuration -->
                        <div class="upload-step" id="step2" style="display: none;">
                            <h6 class="step-title">
                                <span class="step-number">2</span>
                                Configure Data Import
                            </h6>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="targetEntity" class="form-label">Target Entity *</label>
                                        <select id="targetEntity" name="TargetEntity" class="form-select" required>
                                            <option value="">Select an entity...</option>
                                            @if (ViewBag.AvailableEntities != null)
                                            {
                                                @foreach (var entity in (List<Technoloway.Core.Models.DatabaseEntityInfo>)ViewBag.AvailableEntities)
                                                {
                                                    <option value="@entity.EntityName">@entity.DisplayName</option>
                                                }
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="operation" class="form-label">Operation *</label>
                                        <select id="operation" name="Operation" class="form-select" required>
                                            <option value="">Select operation...</option>
                                            <option value="Insert">Insert - Add new records</option>
                                            <option value="Update">Update - Modify existing records</option>
                                            <option value="Upsert">Upsert - Insert or update records</option>
                                            <option value="TruncateAndInsert">Truncate & Insert - Replace all data</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check mb-3">
                                        <input type="checkbox" id="hasHeaders" name="HasHeaders" class="form-check-input" checked>
                                        <label for="hasHeaders" class="form-check-label">File has headers</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check mb-3">
                                        <input type="checkbox" id="validateData" name="ValidateData" class="form-check-input" checked>
                                        <label for="validateData" class="form-check-label">Validate data before import</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check mb-3">
                                        <input type="checkbox" id="createBackup" name="CreateBackup" class="form-check-input" checked>
                                        <label for="createBackup" class="form-check-label">Create backup for rollback</label>
                                    </div>
                                </div>
                            </div>

                            <!-- Data Preview -->
                            <div id="dataPreview" class="mb-4" style="display: none;">
                                <h6>Data Preview</h6>
                                <div class="table-responsive">
                                    <table id="previewTable" class="table table-sm table-bordered">
                                        <thead class="table-light"></thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                                <div id="previewSummary" class="text-muted"></div>
                            </div>

                            <div class="mt-3">
                                <button type="button" id="backStep1" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>Back
                                </button>
                                <button type="button" id="nextStep2" class="btn btn-primary ms-2" disabled>
                                    Next <i class="fas fa-arrow-right ms-1"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Step 3: Field Mapping -->
                        <div class="upload-step" id="step3" style="display: none;">
                            <h6 class="step-title">
                                <span class="step-number">3</span>
                                Map Fields
                            </h6>

                            <div id="fieldMapping" class="mb-4">
                                <p class="text-muted">Map your file columns to database fields:</p>
                                <div id="mappingContainer"></div>
                            </div>

                            <div class="mt-3">
                                <button type="button" id="backStep2" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>Back
                                </button>
                                <button type="button" id="validateBtn" class="btn btn-outline-warning ms-2">
                                    <i class="fas fa-check-circle me-1"></i>Validate Data
                                </button>
                                <button type="button" id="nextStep3" class="btn btn-primary ms-2" disabled>
                                    Next <i class="fas fa-arrow-right ms-1"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Step 4: Validation & Confirmation -->
                        <div class="upload-step" id="step4" style="display: none;">
                            <h6 class="step-title">
                                <span class="step-number">4</span>
                                Review & Confirm
                            </h6>

                            <div id="validationResults" class="mb-4"></div>

                            <div class="confirmation-summary">
                                <h6>Upload Summary</h6>
                                <div id="uploadSummary" class="alert alert-info"></div>
                            </div>

                            <div class="mt-3">
                                <button type="button" id="backStep3" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>Back
                                </button>
                                <button type="submit" id="processBtn" class="btn btn-success ms-2">
                                    <i class="fas fa-upload me-1"></i>Process Upload
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Modal -->
    <div class="modal fade" id="resultsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Upload Results</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="resultsContent">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="@Url.Action("History")" class="btn btn-primary">View History</a>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="~/css/admin/data-upload.css" asp-append-version="true" />
}

@section Scripts {
    <script src="~/js/admin/data-upload.js" asp-append-version="true"></script>
}
