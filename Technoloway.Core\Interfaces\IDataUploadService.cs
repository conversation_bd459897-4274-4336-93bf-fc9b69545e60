using Microsoft.AspNetCore.Http;
using Technoloway.Core.Models;
using Technoloway.Core.Entities;

namespace Technoloway.Core.Interfaces;

public interface IDataUploadService
{
    /// <summary>
    /// Validates if the uploaded file is supported and safe
    /// </summary>
    Task<(bool IsValid, string ErrorMessage)> ValidateFileAsync(IFormFile file);

    /// <summary>
    /// Parses the uploaded file and returns a preview of the data
    /// </summary>
    Task<FilePreviewModel> ParseFilePreviewAsync(IFormFile file, bool hasHeaders = true, string? delimiter = null);

    /// <summary>
    /// Gets available database entities that can be used for data upload
    /// </summary>
    Task<List<DatabaseEntityInfo>> GetAvailableEntitiesAsync();

    /// <summary>
    /// Gets detailed information about a specific entity including its fields
    /// </summary>
    Task<DatabaseEntityInfo?> GetEntityInfoAsync(string entityName);

    /// <summary>
    /// Validates the field mappings and data before processing
    /// </summary>
    Task<ValidationResult> ValidateDataAsync(IFormFile file, string targetEntity, Dictionary<string, string> fieldMappings, bool hasHeaders = true);

    /// <summary>
    /// Processes the data upload with the specified operation
    /// </summary>
    Task<DataUploadResult> ProcessDataUploadAsync(
        IFormFile file, 
        string targetEntity, 
        DataUploadOperation operation,
        Dictionary<string, string> fieldMappings,
        string userId,
        string userName,
        bool hasHeaders = true,
        bool validateData = true,
        bool createBackup = true);

    /// <summary>
    /// Gets the upload history/logs
    /// </summary>
    Task<List<DataUploadLog>> GetUploadHistoryAsync(int page = 1, int pageSize = 20);

    /// <summary>
    /// Gets a specific upload log by ID
    /// </summary>
    Task<DataUploadLog?> GetUploadLogAsync(int logId);

    /// <summary>
    /// Rolls back a data upload operation if possible
    /// </summary>
    Task<(bool Success, string Message)> RollbackUploadAsync(int logId, string userId);

    /// <summary>
    /// Deletes old upload logs and associated files
    /// </summary>
    Task CleanupOldLogsAsync(int daysToKeep = 30);
}

public interface IDataParsingService
{
    /// <summary>
    /// Parses CSV files
    /// </summary>
    Task<(List<Dictionary<string, object>> Data, List<string> Headers)> ParseCsvAsync(Stream stream, bool hasHeaders = true, string delimiter = ",");

    /// <summary>
    /// Parses Excel files (.xlsx, .xls)
    /// </summary>
    Task<(List<Dictionary<string, object>> Data, List<string> Headers)> ParseExcelAsync(Stream stream, bool hasHeaders = true, int sheetIndex = 0);

    /// <summary>
    /// Parses JSON files
    /// </summary>
    Task<(List<Dictionary<string, object>> Data, List<string> Headers)> ParseJsonAsync(Stream stream);

    /// <summary>
    /// Parses XML files
    /// </summary>
    Task<(List<Dictionary<string, object>> Data, List<string> Headers)> ParseXmlAsync(Stream stream);

    /// <summary>
    /// Parses SQL files (INSERT statements)
    /// </summary>
    Task<(List<Dictionary<string, object>> Data, List<string> Headers)> ParseSqlAsync(Stream stream, string tableName);

    /// <summary>
    /// Detects the file type based on content and extension
    /// </summary>
    string DetectFileType(string fileName, Stream stream);

    /// <summary>
    /// Validates file content for security threats
    /// </summary>
    Task<(bool IsValid, List<string> Issues)> ValidateFileContentAsync(Stream stream, string fileType);
}

public interface IDatabaseOperationService
{
    /// <summary>
    /// Performs insert operation
    /// </summary>
    Task<(int Successful, int Failed, List<string> Errors)> InsertDataAsync(string entityName, List<Dictionary<string, object>> data, Dictionary<string, string> fieldMappings);

    /// <summary>
    /// Performs update operation
    /// </summary>
    Task<(int Successful, int Failed, List<string> Errors)> UpdateDataAsync(string entityName, List<Dictionary<string, object>> data, Dictionary<string, string> fieldMappings, string keyField);

    /// <summary>
    /// Performs upsert operation (insert or update)
    /// </summary>
    Task<(int Successful, int Failed, List<string> Errors)> UpsertDataAsync(string entityName, List<Dictionary<string, object>> data, Dictionary<string, string> fieldMappings, string keyField);

    /// <summary>
    /// Truncates table and inserts new data
    /// </summary>
    Task<(int Successful, int Failed, List<string> Errors)> TruncateAndInsertAsync(string entityName, List<Dictionary<string, object>> data, Dictionary<string, string> fieldMappings);

    /// <summary>
    /// Creates a backup of the target table before operations
    /// </summary>
    Task<string> CreateBackupAsync(string entityName);

    /// <summary>
    /// Restores data from backup
    /// </summary>
    Task<bool> RestoreFromBackupAsync(string entityName, string backupFilePath);

    /// <summary>
    /// Gets entity metadata
    /// </summary>
    Task<DatabaseEntityInfo> GetEntityMetadataAsync(string entityName);

    /// <summary>
    /// Validates data against entity constraints
    /// </summary>
    Task<ValidationResult> ValidateDataConstraintsAsync(string entityName, List<Dictionary<string, object>> data, Dictionary<string, string> fieldMappings);
}
